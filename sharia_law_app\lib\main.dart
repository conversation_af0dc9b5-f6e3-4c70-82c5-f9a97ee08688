import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'theme/app_theme.dart';
import 'screens/home_screen.dart';

void main() {
  runApp(const ShariaLawApp());
}

class ShariaLawApp extends StatelessWidget {
  const ShariaLawApp({super.key});

  @override
  Widget build(BuildContext context) {
    // تعيين اتجاه النص للعربية
    return MaterialApp(
      title: 'تطبيق الشريعة والقانون',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,

      // دعم اللغة العربية
      locale: const Locale('ar', 'SA'),

      home: const HomeScreen(),

      // إعدادات شريط الحالة
      builder: (context, child) {
        SystemChrome.setSystemUIOverlayStyle(
          const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.dark,
            systemNavigationBarColor: Colors.white,
            systemNavigationBarIconBrightness: Brightness.dark,
          ),
        );

        return Directionality(textDirection: TextDirection.rtl, child: child!);
      },
    );
  }
}
