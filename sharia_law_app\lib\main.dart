import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

void main() {
  runApp(const ShariaLawApp());
}

class ShariaLawApp extends StatelessWidget {
  const ShariaLawApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'تطبيق الشريعة والقانون',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF6366F1),
          brightness: Brightness.light,
        ),
        fontFamily: 'Arial',
      ),

      locale: const Locale('ar', 'SA'),
      home: const HomeScreen(),

      builder: (context, child) {
        SystemChrome.setSystemUIOverlayStyle(
          const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.dark,
            systemNavigationBarColor: Colors.white,
            systemNavigationBarIconBrightness: Brightness.dark,
          ),
        );

        return Directionality(textDirection: TextDirection.rtl, child: child!);
      },
    );
  }
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<AcademicYear> academicYears = [
    AcademicYear(
      id: 'year1',
      name: 'الفرقة الأولى',
      subjects: 12,
      color: const Color(0xFF6366F1),
      semesters: [
        Semester(
          id: 'year1_sem1',
          name: 'الترم الأول',
          subjects: [
            Subject(name: 'قضايا فقهية معاصرة', lectures: 2, tests: 3),
            Subject(name: 'تاريخ التشريع', lectures: 2, tests: 4),
            Subject(name: 'مصطلح الحديث ورجاله', lectures: 1, tests: 3),
            Subject(name: 'علم الإجرام والعقاب', lectures: 2, tests: 2),
            Subject(name: 'تاريخ القانون', lectures: 1, tests: 3),
          ],
        ),
        Semester(
          id: 'year1_sem2',
          name: 'الترم الثاني',
          subjects: [
            Subject(name: 'القرآن الكريم', lectures: 2, tests: 3),
            Subject(name: 'القانون الدولي العام', lectures: 2, tests: 4),
            Subject(
              name: 'أصول الفقه (حنفي / غير حنفي)',
              lectures: 1,
              tests: 3,
            ),
            Subject(name: 'اللغة الأجنبية', lectures: 2, tests: 2),
            Subject(name: 'النظم السياسية والدستورية', lectures: 1, tests: 3),
            Subject(name: 'المدخل للعلوم القانونية', lectures: 2, tests: 4),
            Subject(
              name: 'فقه مذهبي (حنفي - مالكي - شافعي)',
              lectures: 1,
              tests: 3,
            ),
          ],
        ),
      ],
    ),
    AcademicYear(
      id: 'year2',
      name: 'الفرقة الثانية',
      subjects: 11,
      color: const Color(0xFFEC4899),
      semesters: [
        Semester(
          id: 'year2_sem1',
          name: 'الترم الأول',
          subjects: [
            Subject(name: 'المنظمات الدولية', lectures: 2, tests: 3),
            Subject(name: 'فقه مقارن', lectures: 2, tests: 4),
            Subject(name: 'الاقتصاد', lectures: 1, tests: 3),
            Subject(name: 'قضايا فقهية معاصرة', lectures: 2, tests: 2),
          ],
        ),
        Semester(
          id: 'year2_sem2',
          name: 'الترم الثاني',
          subjects: [
            Subject(name: 'القرآن الكريم', lectures: 2, tests: 3),
            Subject(name: 'القانون المدني', lectures: 2, tests: 4),
            Subject(name: 'اللغة الأجنبية', lectures: 1, tests: 3),
            Subject(name: 'القانون الإداري', lectures: 2, tests: 2),
            Subject(name: 'القانون الجنائي', lectures: 1, tests: 3),
            Subject(name: 'الفقه المقارن', lectures: 2, tests: 4),
            Subject(
              name: 'أصول الفقه (حنفي / غير حنفي)',
              lectures: 1,
              tests: 3,
            ),
          ],
        ),
      ],
    ),
    AcademicYear(
      id: 'year3',
      name: 'الفرقة الثالثة',
      subjects: 13,
      color: const Color(0xFF10B981),
      semesters: [
        Semester(
          id: 'year3_sem1',
          name: 'الترم الأول',
          subjects: [
            Subject(name: 'القانون المدني (عقود)', lectures: 2, tests: 3),
            Subject(name: 'النقود والبنوك', lectures: 2, tests: 4),
            Subject(name: 'الفقه المقارن', lectures: 1, tests: 3),
            Subject(name: 'المصطلحات القانونية', lectures: 2, tests: 2),
          ],
        ),
        Semester(
          id: 'year3_sem2',
          name: 'الترم الثاني',
          subjects: [
            Subject(name: 'القرآن الكريم', lectures: 2, tests: 3),
            Subject(
              name: 'الفقه المذهبي (حنفي - مالكي - شافعي)',
              lectures: 2,
              tests: 4,
            ),
            Subject(name: 'الفقه المقارن', lectures: 1, tests: 3),
            Subject(name: 'القانون الجنائي', lectures: 2, tests: 2),
            Subject(name: 'قانون المرافعات', lectures: 1, tests: 3),
            Subject(name: 'قضاء إداري', lectures: 2, tests: 4),
            Subject(name: 'قانون تجاري', lectures: 1, tests: 3),
            Subject(
              name: 'أصول الفقه (حنفي / غير حنفي)',
              lectures: 2,
              tests: 2,
            ),
            Subject(name: 'أحوال شخصية للمسلمين', lectures: 1, tests: 3),
          ],
        ),
      ],
    ),
    AcademicYear(
      id: 'year4',
      name: 'الفرقة الرابعة',
      subjects: 14,
      color: const Color(0xFFF59E0B),
      semesters: [
        Semester(
          id: 'year4_sem1',
          name: 'الترم الأول',
          subjects: [
            Subject(name: 'التنفيذ الجبري', lectures: 2, tests: 3),
            Subject(name: 'القانون التجاري', lectures: 2, tests: 4),
            Subject(name: 'العقود الإدارية', lectures: 1, tests: 3),
            Subject(name: 'الفقه المقارن', lectures: 2, tests: 2),
            Subject(name: 'الميراث', lectures: 1, tests: 3),
          ],
        ),
        Semester(
          id: 'year4_sem2',
          name: 'الترم الثاني',
          subjects: [
            Subject(name: 'القرآن الكريم', lectures: 2, tests: 3),
            Subject(name: 'الملكية والتأمينات', lectures: 2, tests: 4),
            Subject(name: 'الإجراءات الجنائية', lectures: 1, tests: 3),
            Subject(name: 'قانون العمل', lectures: 2, tests: 2),
            Subject(
              name: 'المالية العامة والتشريع الضريبي',
              lectures: 1,
              tests: 3,
            ),
            Subject(name: 'القانون الدولي الخاص', lectures: 2, tests: 4),
            Subject(
              name: 'أصول الفقه (حنفي / غير حنفي)',
              lectures: 1,
              tests: 3,
            ),
            Subject(
              name: 'الفقه (مالكي - شافعي - حنبلي)',
              lectures: 2,
              tests: 2,
            ),
            Subject(name: 'أحوال شخصية لغير المسلمين', lectures: 1, tests: 3),
          ],
        ),
      ],
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              // الهيدر
              SliverToBoxAdapter(
                child: Container(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 16,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              const Color(0xFF6366F1).withValues(alpha: 0.1),
                              const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: const Color(
                              0xFF6366F1,
                            ).withValues(alpha: 0.2),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            Text(
                              'مرحباً بك!',
                              style: Theme.of(
                                context,
                              ).textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: const Color(0xFF6366F1),
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'اختر الفرقة الدراسية للاطلاع على المواد والمحاضرات',
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(color: const Color(0xFF64748B)),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 20),

                      // إحصائيات سريعة
                      Row(
                        children: [
                          Expanded(
                            child: _buildStatCard(
                              icon: Icons.school_outlined,
                              title: '4',
                              subtitle: 'فرق دراسية',
                              color: const Color(0xFF6366F1),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildStatCard(
                              icon: Icons.book_outlined,
                              title: '50',
                              subtitle: 'مادة دراسية',
                              color: const Color(0xFFEC4899),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildStatCard(
                              icon: Icons.picture_as_pdf_outlined,
                              title: '100+',
                              subtitle: 'ملف PDF',
                              color: const Color(0xFF8B5CF6),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // قائمة الفرق الدراسية
              SliverPadding(
                padding: const EdgeInsets.all(20),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate((context, index) {
                    final year = academicYears[index];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: YearCard(
                        year: year,
                        index: index,
                        onTap: () => _navigateToSubjects(year),
                      ),
                    );
                  }, childCount: academicYears.length),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.1), width: 1),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _navigateToSubjects(AcademicYear year) {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => SubjectsScreen(year: year)));
  }
}

class YearCard extends StatelessWidget {
  final AcademicYear year;
  final int index;
  final VoidCallback onTap;

  const YearCard({
    super.key,
    required this.year,
    required this.index,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 100,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [year.color, year.color.withValues(alpha: 0.8)],
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: year.color.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 16),

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,

                  children: [
                    Text(
                      year.name,
                      style: Theme.of(
                        context,
                      ).textTheme.headlineMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.book_outlined,
                          color: Colors.white.withValues(alpha: 0.8),
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${year.subjects} مادة دراسية',
                          style: Theme.of(
                            context,
                          ).textTheme.bodySmall?.copyWith(
                            color: Colors.white.withValues(alpha: 0.8),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              Icon(Icons.arrow_forward_ios, color: Colors.white, size: 16),
            ],
          ),
        ),
      ),
    );
  }
}

class SubjectsScreen extends StatefulWidget {
  final AcademicYear year;

  const SubjectsScreen({super.key, required this.year});

  @override
  State<SubjectsScreen> createState() => _SubjectsScreenState();
}

class _SubjectsScreenState extends State<SubjectsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: widget.year.semesters.length,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: SafeArea(
        child: Column(
          children: [
            // Header with gradient
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    widget.year.color,
                    widget.year.color.withValues(alpha: 0.8),
                  ],
                ),
              ),
              child: Column(
                children: [
                  // App Bar
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(
                            Icons.arrow_back_ios,
                            color: Colors.white,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            widget.year.name,
                            style: Theme.of(
                              context,
                            ).textTheme.headlineMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        const SizedBox(width: 48), // Balance the back button
                      ],
                    ),
                  ),

                  // Tab Bar
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: TabBar(
                      controller: _tabController,
                      indicator: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(25),
                      ),
                      labelColor: widget.year.color,
                      unselectedLabelColor: Colors.white,
                      labelStyle: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      unselectedLabelStyle: const TextStyle(
                        fontWeight: FontWeight.normal,
                        fontSize: 14,
                      ),
                      tabs:
                          widget.year.semesters
                              .map((semester) => Tab(text: semester.name))
                              .toList(),
                    ),
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),

            // Tab Bar View
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children:
                    widget.year.semesters
                        .map((semester) => _buildSemesterView(semester))
                        .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSemesterView(Semester semester) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: semester.subjects.length,
      itemBuilder: (context, index) {
        final subject = semester.subjects[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildSubjectCard(subject),
        );
      },
    );
  }

  Widget _buildSubjectCard(Subject subject) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: widget.year.color.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Subject Icon
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: widget.year.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.book_outlined,
                color: widget.year.color,
                size: 24,
              ),
            ),

            const SizedBox(width: 16),

            // Subject Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    subject.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF1F2937),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      _buildInfoChip(
                        icon: Icons.play_circle_outline,
                        text: '${subject.lectures} محاضرات',
                        color: const Color(0xFF10B981),
                      ),
                      const SizedBox(width: 12),
                      _buildInfoChip(
                        icon: Icons.quiz_outlined,
                        text: '${subject.tests} اختبارات',
                        color: const Color(0xFFEC4899),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Arrow Icon
            Icon(
              Icons.arrow_forward_ios,
              color: widget.year.color.withValues(alpha: 0.6),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

class Subject {
  final String name;
  final int lectures;
  final int tests;

  Subject({required this.name, required this.lectures, required this.tests});
}

class Semester {
  final String id;
  final String name;
  final List<Subject> subjects;

  Semester({required this.id, required this.name, required this.subjects});
}

class AcademicYear {
  final String id;
  final String name;
  final int subjects;
  final Color color;
  final List<Semester> semesters;

  AcademicYear({
    required this.id,
    required this.name,
    required this.subjects,
    required this.color,
    required this.semesters,
  });
}
