import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

void main() {
  runApp(const ShariaLawApp());
}

class ShariaLawApp extends StatelessWidget {
  const ShariaLawApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'تطبيق الشريعة والقانون',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF6366F1),
          brightness: Brightness.light,
        ),
        fontFamily: 'Arial',
      ),

      locale: const Locale('ar', 'SA'),
      home: const MainScreen(),

      builder: (context, child) {
        SystemChrome.setSystemUIOverlayStyle(
          const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.dark,
            systemNavigationBarColor: Colors.white,
            systemNavigationBarIconBrightness: Brightness.dark,
          ),
        );

        return Directionality(textDirection: TextDirection.rtl, child: child!);
      },
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const HomeScreen(),
    const CommunityScreen(),
    const ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.white,
          selectedItemColor: const Color(0xFF6366F1),
          unselectedItemColor: const Color(0xFF9CA3AF),
          selectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.normal,
            fontSize: 11,
          ),
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.home_outlined),
              activeIcon: Icon(Icons.home),
              label: 'الرئيسية',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.people_outline),
              activeIcon: Icon(Icons.people),
              label: 'المجتمع',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.person_outline),
              activeIcon: Icon(Icons.person),
              label: 'الملف الشخصي',
            ),
          ],
        ),
      ),
    );
  }
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<AcademicYear> academicYears = [
    AcademicYear(
      id: 'year1',
      name: 'الفرقة الأولى',
      subjects: 12,
      color: const Color(0xFF6366F1),
      semesters: [
        Semester(
          id: 'year1_sem1',
          name: 'الترم الأول',
          subjects: [
            Subject(name: 'قضايا فقهية معاصرة', lectures: 2, tests: 3),
            Subject(name: 'تاريخ التشريع', lectures: 2, tests: 4),
            Subject(name: 'مصطلح الحديث ورجاله', lectures: 1, tests: 3),
            Subject(name: 'علم الإجرام والعقاب', lectures: 2, tests: 2),
            Subject(name: 'تاريخ القانون', lectures: 1, tests: 3),
          ],
        ),
        Semester(
          id: 'year1_sem2',
          name: 'الترم الثاني',
          subjects: [
            Subject(name: 'القرآن الكريم', lectures: 2, tests: 3),
            Subject(name: 'القانون الدولي العام', lectures: 2, tests: 4),
            Subject(
              name: 'أصول الفقه (حنفي / غير حنفي)',
              lectures: 1,
              tests: 3,
            ),
            Subject(name: 'اللغة الأجنبية', lectures: 2, tests: 2),
            Subject(name: 'النظم السياسية والدستورية', lectures: 1, tests: 3),
            Subject(name: 'المدخل للعلوم القانونية', lectures: 2, tests: 4),
            Subject(
              name: 'فقه مذهبي (حنفي - مالكي - شافعي)',
              lectures: 1,
              tests: 3,
            ),
          ],
        ),
      ],
    ),
    AcademicYear(
      id: 'year2',
      name: 'الفرقة الثانية',
      subjects: 11,
      color: const Color(0xFFEC4899),
      semesters: [
        Semester(
          id: 'year2_sem1',
          name: 'الترم الأول',
          subjects: [
            Subject(name: 'المنظمات الدولية', lectures: 2, tests: 3),
            Subject(name: 'فقه مقارن', lectures: 2, tests: 4),
            Subject(name: 'الاقتصاد', lectures: 1, tests: 3),
            Subject(name: 'قضايا فقهية معاصرة', lectures: 2, tests: 2),
          ],
        ),
        Semester(
          id: 'year2_sem2',
          name: 'الترم الثاني',
          subjects: [
            Subject(name: 'القرآن الكريم', lectures: 2, tests: 3),
            Subject(name: 'القانون المدني', lectures: 2, tests: 4),
            Subject(name: 'اللغة الأجنبية', lectures: 1, tests: 3),
            Subject(name: 'القانون الإداري', lectures: 2, tests: 2),
            Subject(name: 'القانون الجنائي', lectures: 1, tests: 3),
            Subject(name: 'الفقه المقارن', lectures: 2, tests: 4),
            Subject(
              name: 'أصول الفقه (حنفي / غير حنفي)',
              lectures: 1,
              tests: 3,
            ),
          ],
        ),
      ],
    ),
    AcademicYear(
      id: 'year3',
      name: 'الفرقة الثالثة',
      subjects: 13,
      color: const Color(0xFF10B981),
      semesters: [
        Semester(
          id: 'year3_sem1',
          name: 'الترم الأول',
          subjects: [
            Subject(name: 'القانون المدني (عقود)', lectures: 2, tests: 3),
            Subject(name: 'النقود والبنوك', lectures: 2, tests: 4),
            Subject(name: 'الفقه المقارن', lectures: 1, tests: 3),
            Subject(name: 'المصطلحات القانونية', lectures: 2, tests: 2),
          ],
        ),
        Semester(
          id: 'year3_sem2',
          name: 'الترم الثاني',
          subjects: [
            Subject(name: 'القرآن الكريم', lectures: 2, tests: 3),
            Subject(
              name: 'الفقه المذهبي (حنفي - مالكي - شافعي)',
              lectures: 2,
              tests: 4,
            ),
            Subject(name: 'الفقه المقارن', lectures: 1, tests: 3),
            Subject(name: 'القانون الجنائي', lectures: 2, tests: 2),
            Subject(name: 'قانون المرافعات', lectures: 1, tests: 3),
            Subject(name: 'قضاء إداري', lectures: 2, tests: 4),
            Subject(name: 'قانون تجاري', lectures: 1, tests: 3),
            Subject(
              name: 'أصول الفقه (حنفي / غير حنفي)',
              lectures: 2,
              tests: 2,
            ),
            Subject(name: 'أحوال شخصية للمسلمين', lectures: 1, tests: 3),
          ],
        ),
      ],
    ),
    AcademicYear(
      id: 'year4',
      name: 'الفرقة الرابعة',
      subjects: 14,
      color: const Color(0xFFF59E0B),
      semesters: [
        Semester(
          id: 'year4_sem1',
          name: 'الترم الأول',
          subjects: [
            Subject(name: 'التنفيذ الجبري', lectures: 2, tests: 3),
            Subject(name: 'القانون التجاري', lectures: 2, tests: 4),
            Subject(name: 'العقود الإدارية', lectures: 1, tests: 3),
            Subject(name: 'الفقه المقارن', lectures: 2, tests: 2),
            Subject(name: 'الميراث', lectures: 1, tests: 3),
          ],
        ),
        Semester(
          id: 'year4_sem2',
          name: 'الترم الثاني',
          subjects: [
            Subject(name: 'القرآن الكريم', lectures: 2, tests: 3),
            Subject(name: 'الملكية والتأمينات', lectures: 2, tests: 4),
            Subject(name: 'الإجراءات الجنائية', lectures: 1, tests: 3),
            Subject(name: 'قانون العمل', lectures: 2, tests: 2),
            Subject(
              name: 'المالية العامة والتشريع الضريبي',
              lectures: 1,
              tests: 3,
            ),
            Subject(name: 'القانون الدولي الخاص', lectures: 2, tests: 4),
            Subject(
              name: 'أصول الفقه (حنفي / غير حنفي)',
              lectures: 1,
              tests: 3,
            ),
            Subject(
              name: 'الفقه (مالكي - شافعي - حنبلي)',
              lectures: 2,
              tests: 2,
            ),
            Subject(name: 'أحوال شخصية لغير المسلمين', lectures: 1, tests: 3),
          ],
        ),
      ],
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              // الهيدر
              SliverToBoxAdapter(
                child: Container(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 16,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              const Color(0xFF6366F1).withValues(alpha: 0.1),
                              const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: const Color(
                              0xFF6366F1,
                            ).withValues(alpha: 0.2),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            Text(
                              'مرحباً بك!',
                              style: Theme.of(
                                context,
                              ).textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: const Color(0xFF6366F1),
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'اختر الفرقة الدراسية للاطلاع على المواد والمحاضرات',
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(color: const Color(0xFF64748B)),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 20),

                      // إحصائيات سريعة
                      Row(
                        children: [
                          Expanded(
                            child: _buildStatCard(
                              icon: Icons.school_outlined,
                              title: '4',
                              subtitle: 'فرق دراسية',
                              color: const Color(0xFF6366F1),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildStatCard(
                              icon: Icons.book_outlined,
                              title: '50',
                              subtitle: 'مادة دراسية',
                              color: const Color(0xFFEC4899),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildStatCard(
                              icon: Icons.picture_as_pdf_outlined,
                              title: '100+',
                              subtitle: 'ملف PDF',
                              color: const Color(0xFF8B5CF6),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // قائمة الفرق الدراسية
              SliverPadding(
                padding: const EdgeInsets.all(20),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate((context, index) {
                    final year = academicYears[index];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: YearCard(
                        year: year,
                        index: index,
                        onTap: () => _navigateToSubjects(year),
                      ),
                    );
                  }, childCount: academicYears.length),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.1), width: 1),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _navigateToSubjects(AcademicYear year) {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => SubjectsScreen(year: year)));
  }
}

class YearCard extends StatelessWidget {
  final AcademicYear year;
  final int index;
  final VoidCallback onTap;

  const YearCard({
    super.key,
    required this.year,
    required this.index,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 100,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [year.color, year.color.withValues(alpha: 0.8)],
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: year.color.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 16),

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,

                  children: [
                    Text(
                      year.name,
                      style: Theme.of(
                        context,
                      ).textTheme.headlineMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.book_outlined,
                          color: Colors.white.withValues(alpha: 0.8),
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${year.subjects} مادة دراسية',
                          style: Theme.of(
                            context,
                          ).textTheme.bodySmall?.copyWith(
                            color: Colors.white.withValues(alpha: 0.8),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              Icon(Icons.arrow_forward_ios, color: Colors.white, size: 16),
            ],
          ),
        ),
      ),
    );
  }
}

class SubjectsScreen extends StatefulWidget {
  final AcademicYear year;

  const SubjectsScreen({super.key, required this.year});

  @override
  State<SubjectsScreen> createState() => _SubjectsScreenState();
}

class _SubjectsScreenState extends State<SubjectsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: widget.year.semesters.length,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: SafeArea(
        child: Column(
          children: [
            // Header with gradient
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    widget.year.color,
                    widget.year.color.withValues(alpha: 0.8),
                  ],
                ),
              ),
              child: Column(
                children: [
                  // App Bar
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(
                            Icons.arrow_back_ios,
                            color: Colors.white,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            widget.year.name,
                            style: Theme.of(
                              context,
                            ).textTheme.headlineMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        const SizedBox(width: 48), // Balance the back button
                      ],
                    ),
                  ),

                  // Tab Bar
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: TabBar(
                      controller: _tabController,
                      indicator: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(25),
                      ),
                      labelColor: widget.year.color,
                      unselectedLabelColor: Colors.white,
                      labelStyle: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      unselectedLabelStyle: const TextStyle(
                        fontWeight: FontWeight.normal,
                        fontSize: 14,
                      ),
                      tabs:
                          widget.year.semesters
                              .map((semester) => Tab(text: semester.name))
                              .toList(),
                    ),
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),

            // Tab Bar View
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children:
                    widget.year.semesters
                        .map((semester) => _buildSemesterView(semester))
                        .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSemesterView(Semester semester) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: semester.subjects.length,
      itemBuilder: (context, index) {
        final subject = semester.subjects[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildSubjectCard(subject),
        );
      },
    );
  }

  Widget _buildSubjectCard(Subject subject) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: widget.year.color.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Subject Icon
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: widget.year.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.book_outlined,
                color: widget.year.color,
                size: 24,
              ),
            ),

            const SizedBox(width: 16),

            // Subject Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    subject.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF1F2937),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      _buildInfoChip(
                        icon: Icons.play_circle_outline,
                        text: '${subject.lectures} محاضرات',
                        color: const Color(0xFF10B981),
                      ),
                      const SizedBox(width: 12),
                      _buildInfoChip(
                        icon: Icons.quiz_outlined,
                        text: '${subject.tests} اختبارات',
                        color: const Color(0xFFEC4899),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Arrow Icon
            Icon(
              Icons.arrow_forward_ios,
              color: widget.year.color.withValues(alpha: 0.6),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

// Community Screen
class CommunityScreen extends StatefulWidget {
  const CommunityScreen({super.key});

  @override
  State<CommunityScreen> createState() => _CommunityScreenState();
}

class _CommunityScreenState extends State<CommunityScreen> {
  final TextEditingController _postController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  final List<Post> _posts = [
    Post(
      id: '1',
      authorName: 'أحمد محمد السيد',
      authorAvatar:
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      content:
          'مرحباً بالجميع في مجتمع كلية الشريعة والقانون! 🎓\n\nهذا المكان مخصص لتبادل المعرفة والخبرات بين الطلاب والأساتذة. نتطلع لمشاركاتكم المفيدة ونقاشاتكم البناءة.\n\n#كلية_الشريعة_والقانون #التعليم_الجامعي',
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      likes: 24,
      comments: 8,
      shares: 3,
      isLiked: false,
      imageUrl:
          'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=600&h=400&fit=crop',
      userRole: 'طالب - الفرقة الثالثة',
    ),
    Post(
      id: '2',
      authorName: 'فاطمة أحمد علي',
      authorAvatar:
          'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      content:
          'السلام عليكم ورحمة الله وبركاته 🌸\n\nهل يمكن لأحد الزملاء مشاركة ملخص محاضرة أصول الفقه الأخيرة؟ لم أتمكن من الحضور بسبب ظروف طارئة.\n\nجزاكم الله خيراً 🤲',
      timestamp: DateTime.now().subtract(const Duration(hours: 5)),
      likes: 12,
      comments: 15,
      shares: 2,
      isLiked: true,
      userRole: 'طالبة - الفرقة الثانية',
    ),
    Post(
      id: '3',
      authorName: 'د. محمد العلي',
      authorAvatar:
          'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      content:
          '📢 إعلان هام للطلاب\n\nتذكير: موعد الامتحان النهائي لمادة القانون المدني:\n📅 يوم الأحد القادم\n🕘 الساعة 9:00 صباحاً\n📍 القاعة الكبرى\n\nأتمنى للجميع التوفيق والنجاح! 🎯\n\n#امتحانات #القانون_المدني',
      timestamp: DateTime.now().subtract(const Duration(days: 1)),
      likes: 67,
      comments: 23,
      shares: 12,
      isLiked: true,
      userRole: 'أستاذ القانون المدني',
      isPinned: true,
    ),
    Post(
      id: '4',
      authorName: 'سارة محمود',
      authorAvatar:
          'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      content:
          'مشاركة مفيدة 📚\n\nقمت بتلخيص أهم النقاط في محاضرة الفقه المقارن اليوم. الملف متاح للجميع في التعليقات.\n\nنسأل الله التوفيق للجميع! 🤲',
      timestamp: DateTime.now().subtract(const Duration(hours: 8)),
      likes: 31,
      comments: 9,
      shares: 7,
      isLiked: false,
      userRole: 'طالبة - الفرقة الرابعة',
      hasAttachment: true,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F2F5),
      body: SafeArea(
        child: Column(
          children: [
            // Facebook-style Header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Text(
                    'المجتمع',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF1877F2),
                      fontSize: 24,
                    ),
                  ),
                  const Spacer(),
                  // Search Button
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: const Color(0xFFF0F2F5),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: IconButton(
                      onPressed: () {},
                      icon: const Icon(
                        Icons.search,
                        color: Color(0xFF65676B),
                        size: 20,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Messages Button
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: const Color(0xFFF0F2F5),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: IconButton(
                      onPressed: () {},
                      icon: const Icon(
                        Icons.chat_bubble_outline,
                        color: Color(0xFF65676B),
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Posts List with Create Post
            Expanded(
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  // Create Post Section
                  SliverToBoxAdapter(
                    child: Container(
                      margin: const EdgeInsets.all(8),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: _buildCreatePostSection(),
                    ),
                  ),

                  // Posts
                  SliverList(
                    delegate: SliverChildBuilderDelegate((context, index) {
                      return Container(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        child: _buildPostCard(_posts[index]),
                      );
                    }, childCount: _posts.length),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreatePostSection() {
    return Row(
      children: [
        // Profile Picture
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: const Color(0xFF1877F2),
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Center(
            child: Text('👤', style: TextStyle(fontSize: 20)),
          ),
        ),
        const SizedBox(width: 12),

        // Create Post Input
        Expanded(
          child: GestureDetector(
            onTap: () => _showCreatePostDialog(),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: const Color(0xFFF0F2F5),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: const Color(0xFFE4E6EA), width: 1),
              ),
              child: Text(
                'ما الذي تفكر فيه؟',
                style: TextStyle(color: const Color(0xFF65676B), fontSize: 16),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPostCard(Post post) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Post Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Profile Picture
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    color: const Color(0xFF1877F2).withValues(alpha: 0.1),
                  ),
                  child: Center(
                    child: Text('👤', style: const TextStyle(fontSize: 20)),
                  ),
                ),
                const SizedBox(width: 12),

                // Author Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post.authorName,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 15,
                          color: Color(0xFF050505),
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        _formatTimestamp(post.timestamp),
                        style: const TextStyle(
                          color: Color(0xFF65676B),
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),
                ),

                // More Options
                IconButton(
                  onPressed: () {},
                  icon: const Icon(
                    Icons.more_horiz,
                    color: Color(0xFF65676B),
                    size: 20,
                  ),
                ),
              ],
            ),
          ),

          // Post Content
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              post.content,
              style: const TextStyle(
                color: Color(0xFF050505),
                fontSize: 15,
                height: 1.33,
              ),
            ),
          ),

          const SizedBox(height: 12),

          // Reactions Summary
          if (post.likes > 0 || post.comments > 0)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  if (post.likes > 0) ...[
                    Container(
                      width: 18,
                      height: 18,
                      decoration: BoxDecoration(
                        color: const Color(0xFF1877F2),
                        borderRadius: BorderRadius.circular(9),
                      ),
                      child: const Icon(
                        Icons.thumb_up,
                        color: Colors.white,
                        size: 10,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${post.likes}',
                      style: const TextStyle(
                        color: Color(0xFF65676B),
                        fontSize: 13,
                      ),
                    ),
                  ],
                  const Spacer(),
                  if (post.comments > 0)
                    Text(
                      '${post.comments} تعليق',
                      style: const TextStyle(
                        color: Color(0xFF65676B),
                        fontSize: 13,
                      ),
                    ),
                ],
              ),
            ),

          // Divider
          if (post.likes > 0 || post.comments > 0)
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              height: 1,
              color: const Color(0xFFE4E6EA),
            ),

          // Action Buttons
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
            child: Row(
              children: [
                Expanded(
                  child: _buildFacebookActionButton(
                    icon:
                        post.isLiked ? Icons.thumb_up : Icons.thumb_up_outlined,
                    label: 'إعجاب',
                    color:
                        post.isLiked
                            ? const Color(0xFF1877F2)
                            : const Color(0xFF65676B),
                    onTap: () => _toggleLike(post),
                  ),
                ),
                Expanded(
                  child: _buildFacebookActionButton(
                    icon: Icons.chat_bubble_outline,
                    label: 'تعليق',
                    color: const Color(0xFF65676B),
                    onTap: () => _showCommentsDialog(post),
                  ),
                ),
                Expanded(
                  child: _buildFacebookActionButton(
                    icon: Icons.share_outlined,
                    label: 'مشاركة',
                    color: const Color(0xFF65676B),
                    onTap: () => _sharePost(post),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFacebookActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: 15,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  void _showCreatePostDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إنشاء منشور جديد'),
            content: TextField(
              controller: _postController,
              maxLines: 4,
              decoration: const InputDecoration(
                hintText: 'ما الذي تريد مشاركته؟',
                border: OutlineInputBorder(),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  if (_postController.text.isNotEmpty) {
                    _createPost(_postController.text);
                    _postController.clear();
                    Navigator.pop(context);
                  }
                },
                child: const Text('نشر'),
              ),
            ],
          ),
    );
  }

  void _createPost(String content) {
    setState(() {
      _posts.insert(
        0,
        Post(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          authorName: 'أنت',
          authorAvatar: '👤',
          content: content,
          timestamp: DateTime.now(),
          likes: 0,
          comments: 0,
          shares: 0,
          isLiked: false,
        ),
      );
    });
  }

  void _toggleLike(Post post) {
    setState(() {
      post.isLiked = !post.isLiked;
      post.likes += post.isLiked ? 1 : -1;
    });
  }

  void _showCommentsDialog(Post post) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('التعليقات'),
            content: const Text('سيتم إضافة نظام التعليقات قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  void _sharePost(Post post) {
    setState(() {
      post.shares += 1;
    });
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('تم مشاركة المنشور')));
  }
}

// Profile Screen
class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F2F5),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Header
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFF6366F1),
                      const Color(0xFF6366F1).withValues(alpha: 0.8),
                    ],
                  ),
                ),
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        children: [
                          // Profile Picture
                          Container(
                            width: 100,
                            height: 100,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(50),
                            ),
                            child: const Center(
                              child: Text('👤', style: TextStyle(fontSize: 50)),
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Name
                          Text(
                            'أحمد محمد علي',
                            style: Theme.of(
                              context,
                            ).textTheme.headlineMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),

                          // Student Info
                          Text(
                            'طالب - الفرقة الثالثة',
                            style: Theme.of(
                              context,
                            ).textTheme.bodyLarge?.copyWith(
                              color: Colors.white.withValues(alpha: 0.9),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'كلية الشريعة والقانون',
                            style: Theme.of(
                              context,
                            ).textTheme.bodyMedium?.copyWith(
                              color: Colors.white.withValues(alpha: 0.8),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Profile Content
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    // Stats Cards
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            title: '13',
                            subtitle: 'المواد المسجلة',
                            icon: Icons.book_outlined,
                            color: const Color(0xFF10B981),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildStatCard(
                            title: '8.5',
                            subtitle: 'المعدل التراكمي',
                            icon: Icons.star_outline,
                            color: const Color(0xFFF59E0B),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildStatCard(
                            title: '25',
                            subtitle: 'المنشورات',
                            icon: Icons.post_add_outlined,
                            color: const Color(0xFFEC4899),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Menu Items
                    _buildMenuItem(
                      icon: Icons.person_outline,
                      title: 'تعديل الملف الشخصي',
                      onTap: () {},
                    ),
                    _buildMenuItem(
                      icon: Icons.school_outlined,
                      title: 'السجل الأكاديمي',
                      onTap: () {},
                    ),
                    _buildMenuItem(
                      icon: Icons.notifications_none_outlined,
                      title: 'الإشعارات',
                      onTap: () {},
                    ),
                    _buildMenuItem(
                      icon: Icons.settings_outlined,
                      title: 'الإعدادات',
                      onTap: () {},
                    ),
                    _buildMenuItem(
                      icon: Icons.help_outline,
                      title: 'المساعدة',
                      onTap: () {},
                    ),
                    _buildMenuItem(
                      icon: Icons.logout_outlined,
                      title: 'تسجيل الخروج',
                      onTap: () {},
                      isDestructive: true,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1F2937),
            ),
          ),
          Text(
            subtitle,
            style: const TextStyle(fontSize: 12, color: Color(0xFF6B7280)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isDestructive ? Colors.red : const Color(0xFF6366F1),
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isDestructive ? Colors.red : const Color(0xFF1F2937),
            fontWeight: FontWeight.w500,
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: isDestructive ? Colors.red : const Color(0xFF9CA3AF),
        ),
        onTap: onTap,
      ),
    );
  }
}

// Post Model
class Post {
  final String id;
  final String authorName;
  final String authorAvatar;
  final String content;
  final DateTime timestamp;
  int likes;
  int comments;
  int shares;
  bool isLiked;
  final String? imageUrl;
  final String? userRole;
  final bool isPinned;
  final bool hasAttachment;

  Post({
    required this.id,
    required this.authorName,
    required this.authorAvatar,
    required this.content,
    required this.timestamp,
    required this.likes,
    required this.comments,
    required this.shares,
    required this.isLiked,
    this.imageUrl,
    this.userRole,
    this.isPinned = false,
    this.hasAttachment = false,
  });
}

class Subject {
  final String name;
  final int lectures;
  final int tests;

  Subject({required this.name, required this.lectures, required this.tests});
}

class Semester {
  final String id;
  final String name;
  final List<Subject> subjects;

  Semester({required this.id, required this.name, required this.subjects});
}

class AcademicYear {
  final String id;
  final String name;
  final int subjects;
  final Color color;
  final List<Semester> semesters;

  AcademicYear({
    required this.id,
    required this.name,
    required this.subjects,
    required this.color,
    required this.semesters,
  });

  static Widget _buildQuickStat(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Color(0xFF050505),
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Color(0xFF65676B)),
        ),
      ],
    );
  }
}
