import 'package:flutter/material.dart';
import 'dart:ui';

class GlassMorphism extends StatelessWidget {
  final Widget child;
  final double blur;
  final double opacity;
  final Color color;
  final BorderRadius? borderRadius;
  final Border? border;

  const GlassMorphism({
    super.key,
    required this.child,
    this.blur = 10.0,
    this.opacity = 0.1,
    this.color = Colors.white,
    this.borderRadius,
    this.border,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.circular(16),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
        child: Container(
          decoration: BoxDecoration(
            color: color.withOpacity(opacity),
            borderRadius: borderRadius ?? BorderRadius.circular(16),
            border: border ??
                Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 1,
                ),
          ),
          child: child,
        ),
      ),
    );
  }
}

class NeumorphismContainer extends StatelessWidget {
  final Widget child;
  final double borderRadius;
  final Color backgroundColor;
  final double depth;
  final bool isPressed;

  const NeumorphismContainer({
    super.key,
    required this.child,
    this.borderRadius = 16.0,
    this.backgroundColor = const Color(0xFFF0F0F3),
    this.depth = 4.0,
    this.isPressed = false,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 150),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: isPressed
            ? [
                BoxShadow(
                  color: Colors.grey.shade400,
                  offset: Offset(depth / 2, depth / 2),
                  blurRadius: depth,
                  inset: true,
                ),
                BoxShadow(
                  color: Colors.white,
                  offset: Offset(-depth / 2, -depth / 2),
                  blurRadius: depth,
                  inset: true,
                ),
              ]
            : [
                BoxShadow(
                  color: Colors.grey.shade400,
                  offset: Offset(depth, depth),
                  blurRadius: depth * 2,
                ),
                BoxShadow(
                  color: Colors.white,
                  offset: Offset(-depth, -depth),
                  blurRadius: depth * 2,
                ),
              ],
      ),
      child: child,
    );
  }
}

class GradientBorder extends StatelessWidget {
  final Widget child;
  final List<Color> colors;
  final double width;
  final BorderRadius borderRadius;

  const GradientBorder({
    super.key,
    required this.child,
    required this.colors,
    this.width = 2.0,
    this.borderRadius = const BorderRadius.all(Radius.circular(16)),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: colors),
        borderRadius: borderRadius,
      ),
      child: Container(
        margin: EdgeInsets.all(width),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.all(
            Radius.circular(borderRadius.topLeft.x - width),
          ),
        ),
        child: child,
      ),
    );
  }
}

class FloatingParticles extends StatefulWidget {
  final int numberOfParticles;
  final Color particleColor;
  final double minSize;
  final double maxSize;

  const FloatingParticles({
    super.key,
    this.numberOfParticles = 20,
    this.particleColor = Colors.white,
    this.minSize = 2.0,
    this.maxSize = 6.0,
  });

  @override
  State<FloatingParticles> createState() => _FloatingParticlesState();
}

class _FloatingParticlesState extends State<FloatingParticles>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;
  late List<Particle> _particles;

  @override
  void initState() {
    super.initState();
    _initializeParticles();
  }

  void _initializeParticles() {
    _controllers = [];
    _animations = [];
    _particles = [];

    for (int i = 0; i < widget.numberOfParticles; i++) {
      final controller = AnimationController(
        duration: Duration(seconds: 3 + (i % 3)),
        vsync: this,
      );

      final animation = Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOut,
      ));

      _controllers.add(controller);
      _animations.add(animation);
      _particles.add(Particle.random(widget.minSize, widget.maxSize));

      controller.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: List.generate(_particles.length, (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            final particle = _particles[index];
            return Positioned(
              left: particle.x,
              top: particle.y + (_animations[index].value * 50),
              child: Opacity(
                opacity: 0.3 + (_animations[index].value * 0.4),
                child: Container(
                  width: particle.size,
                  height: particle.size,
                  decoration: BoxDecoration(
                    color: widget.particleColor,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            );
          },
        );
      }),
    );
  }
}

class Particle {
  final double x;
  final double y;
  final double size;

  Particle({
    required this.x,
    required this.y,
    required this.size,
  });

  factory Particle.random(double minSize, double maxSize) {
    return Particle(
      x: (400 * (0.5 - (0.5 - 0.5))).abs(),
      y: (800 * (0.5 - (0.5 - 0.5))).abs(),
      size: minSize + ((maxSize - minSize) * 0.5),
    );
  }
}

class WaveAnimation extends StatefulWidget {
  final Color color;
  final double height;
  final double speed;

  const WaveAnimation({
    super.key,
    this.color = Colors.blue,
    this.height = 100.0,
    this.speed = 1.0,
  });

  @override
  State<WaveAnimation> createState() => _WaveAnimationState();
}

class _WaveAnimationState extends State<WaveAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(seconds: (2 / widget.speed).round()),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return CustomPaint(
          painter: WavePainter(
            animationValue: _controller.value,
            color: widget.color,
          ),
          size: Size(double.infinity, widget.height),
        );
      },
    );
  }
}

class WavePainter extends CustomPainter {
  final double animationValue;
  final Color color;

  WavePainter({
    required this.animationValue,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    final waveHeight = size.height * 0.2;
    final waveLength = size.width;

    path.moveTo(0, size.height);

    for (double x = 0; x <= size.width; x++) {
      final y = size.height -
          waveHeight *
              (1 +
                  0.5 *
                      (sin((x / waveLength * 2 * 3.14159) +
                          (animationValue * 2 * 3.14159))));
      path.lineTo(x, y);
    }

    path.lineTo(size.width, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
